import { NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

/**
 * 修复批次管理数据库表结构
 * 创建缺失的batch表，统一批次管理的数据库结构
 */
export async function POST() {
  try {
    console.log('开始修复批次管理数据库表结构...');
    
    // 开始事务
    await run('BEGIN TRANSACTION');
    
    const result: any = {
      actions: [],
      errors: []
    };
    
    try {
      // 1. 检查batch表是否存在
      const batchTableExists = await query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='batch'"
      );
      
      if (!batchTableExists || batchTableExists.length === 0) {
        console.log('创建batch表...');
        
        // 创建batch表，使用中文字段名以保持一致性
        await run(`
          CREATE TABLE batch (
            编号 INTEGER PRIMARY KEY AUTOINCREMENT,
            药品编号 INTEGER NOT NULL,
            批次号 TEXT NOT NULL,
            生产日期 DATE,
            有效期 DATE,
            数量 INTEGER NOT NULL DEFAULT 0,
            剩余数量 INTEGER NOT NULL DEFAULT 0,
            供应商编号 INTEGER,
            成本价 DECIMAL(10,2),
            状态 TEXT DEFAULT 'active' CHECK (状态 IN ('active', 'expired', 'depleted')),
            创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            备注 TEXT,
            FOREIGN KEY (药品编号) REFERENCES 药品信息(编号),
            FOREIGN KEY (供应商编号) REFERENCES 供应商(编号)
          )
        `);
        
        // 创建索引
        await run('CREATE INDEX idx_batch_药品编号 ON batch(药品编号)');
        await run('CREATE INDEX idx_batch_批次号 ON batch(批次号)');
        await run('CREATE INDEX idx_batch_有效期 ON batch(有效期)');
        await run('CREATE INDEX idx_batch_状态 ON batch(状态)');
        
        result.actions.push('创建batch表成功');
        console.log('batch表创建成功');
      } else {
        result.actions.push('batch表已存在，跳过创建');
        console.log('batch表已存在');
      }
      
      // 2. 检查product_batches表是否存在，如果存在则迁移数据到batch表
      const productBatchesExists = await query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='product_batches'"
      );
      
      if (productBatchesExists && productBatchesExists.length > 0) {
        console.log('检查product_batches表数据...');
        
        // 获取product_batches表的数据
        const productBatchesData = await query('SELECT * FROM product_batches');
        
        if (productBatchesData && productBatchesData.length > 0) {
          console.log(`发现${productBatchesData.length}条product_batches数据，开始迁移...`);
          
          // 迁移数据到batch表
          for (const batch of productBatchesData) {
            try {
              await run(`
                INSERT OR IGNORE INTO batch (
                  药品编号, 批次号, 生产日期, 有效期, 数量, 剩余数量, 
                  供应商编号, 成本价, 状态, 备注
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
              `, [
                batch.product_id,
                batch.batch_number,
                batch.production_date,
                batch.expiry_date,
                batch.quantity || 0,
                batch.quantity || 0, // 初始剩余数量等于总数量
                batch.supplier_id,
                batch.cost_price,
                'active', // 默认状态为活跃
                `从product_batches表迁移`
              ]);
            } catch (migrateError) {
              console.error('迁移单条数据失败:', migrateError);
              result.errors.push(`迁移批次数据失败: ${batch.batch_number}`);
            }
          }
          
          result.actions.push(`从product_batches表迁移了${productBatchesData.length}条数据`);
        }
      }
      
      // 3. 确保库存记录表存在且有正确的批次号字段
      const 库存记录TableExists = await query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='库存记录'"
      );
      
      if (!库存记录TableExists || 库存记录TableExists.length === 0) {
        console.log('创建库存记录表...');
        
        await run(`
          CREATE TABLE 库存记录 (
            编号 INTEGER PRIMARY KEY AUTOINCREMENT,
            药品编号 INTEGER NOT NULL,
            操作类型 TEXT NOT NULL CHECK (操作类型 IN ('入库', '出库', '盘点', '调整')),
            数量变化 INTEGER NOT NULL,
            操作前库存 INTEGER NOT NULL DEFAULT 0,
            操作后库存 INTEGER NOT NULL DEFAULT 0,
            供应商编号 INTEGER,
            批次号 TEXT,
            有效期 DATE,
            成本价 DECIMAL(10,2),
            操作人 TEXT,
            操作时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            备注 TEXT,
            码上放心单据号 TEXT,
            码上放心上传状态 TEXT CHECK (码上放心上传状态 IN ('pending', 'success', 'failed')),
            码上放心上传时间 DATETIME,
            码上放心响应 TEXT,
            FOREIGN KEY (药品编号) REFERENCES 药品信息(编号),
            FOREIGN KEY (供应商编号) REFERENCES 供应商(编号)
          )
        `);
        
        result.actions.push('创建库存记录表成功');
      } else {
        result.actions.push('库存记录表已存在');
      }
      
      // 4. 创建批次视图，统一批次查询接口
      await run('DROP VIEW IF EXISTS batch_view');
      await run(`
        CREATE VIEW batch_view AS
        SELECT 
          b.编号 as id,
          b.药品编号 as product_id,
          b.批次号 as batch_number,
          b.生产日期 as production_date,
          b.有效期 as expiry_date,
          b.数量 as quantity,
          b.剩余数量 as remaining_quantity,
          b.供应商编号 as supplier_id,
          s.名称 as supplier_name,
          b.成本价 as purchase_price,
          b.状态 as status,
          b.创建时间 as created_at,
          b.更新时间 as updated_at,
          b.备注 as notes,
          p.名称 as product_name,
          p.规格 as specification
        FROM batch b
        LEFT JOIN 供应商 s ON b.供应商编号 = s.编号
        LEFT JOIN 药品信息 p ON b.药品编号 = p.编号
      `);
      
      result.actions.push('创建批次视图成功');
      
      // 提交事务
      await run('COMMIT');
      
      console.log('批次管理数据库表结构修复完成');
      
      return NextResponse.json({
        success: true,
        message: '批次管理数据库表结构修复成功',
        data: result
      });
      
    } catch (error) {
      // 回滚事务
      await run('ROLLBACK');
      throw error;
    }
    
  } catch (error) {
    console.error('修复批次管理数据库表结构失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '修复批次管理数据库表结构失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
