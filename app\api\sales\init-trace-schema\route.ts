import { NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

/**
 * 初始化销售管理中药品追溯码相关的数据库表结构
 */
export async function POST() {
  try {
    console.log('开始初始化销售管理追溯码数据库表结构...');

    // 开始事务
    await run('BEGIN TRANSACTION');

    const result = {
      actions: [],
      errors: []
    };

    try {
      // 1. 确保药品批次表存在
      const batchTableExists = await query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='batch'"
      );

      if (!batchTableExists || batchTableExists.length === 0) {
        console.log('创建batch表...');
        
        await run(`
          CREATE TABLE batch (
            编号 INTEGER PRIMARY KEY AUTOINCREMENT,
            药品编号 INTEGER NOT NULL,
            批次号 TEXT NOT NULL,
            生产日期 DATE,
            有效期 DATE,
            数量 INTEGER NOT NULL DEFAULT 0,
            剩余数量 INTEGER NOT NULL DEFAULT 0,
            供应商编号 INTEGER,
            成本价 DECIMAL(10,2),
            状态 TEXT DEFAULT 'active' CHECK (状态 IN ('active', 'expired', 'depleted')),
            创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            备注 TEXT,
            FOREIGN KEY (药品编号) REFERENCES 药品信息(编号),
            FOREIGN KEY (供应商编号) REFERENCES 供应商(编号)
          )
        `);
        
        // 创建索引
        await run('CREATE INDEX idx_batch_药品编号 ON batch(药品编号)');
        await run('CREATE INDEX idx_batch_批次号 ON batch(批次号)');
        await run('CREATE INDEX idx_batch_有效期 ON batch(有效期)');
        await run('CREATE INDEX idx_batch_状态 ON batch(状态)');
        
        result.actions.push('创建batch表成功');
      } else {
        result.actions.push('batch表已存在');
      }

      // 2. 确保药品追溯码记录表存在
      const traceTableExists = await query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='药品追溯码记录'"
      );

      if (!traceTableExists || traceTableExists.length === 0) {
        console.log('创建药品追溯码记录表...');
        
        await run(`
          CREATE TABLE 药品追溯码记录 (
            编号 INTEGER PRIMARY KEY AUTOINCREMENT,
            库存记录编号 INTEGER,
            销售订单编号 INTEGER,
            药品编号 INTEGER NOT NULL,
            追溯码 TEXT NOT NULL,
            操作类型 TEXT NOT NULL CHECK (操作类型 IN ('入库', '出库', '销售')),
            批次号 TEXT,
            有效期 DATE,
            生产厂家 TEXT,
            药品名称 TEXT,
            规格 TEXT,
            操作时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            码上放心上传状态 TEXT CHECK (码上放心上传状态 IN ('pending', 'success', 'failed')) DEFAULT 'pending',
            码上放心响应 TEXT,
            备注 TEXT,
            FOREIGN KEY (库存记录编号) REFERENCES 库存记录(编号),
            FOREIGN KEY (销售订单编号) REFERENCES 销售订单(编号),
            FOREIGN KEY (药品编号) REFERENCES 药品信息(编号)
          )
        `);
        
        // 创建索引
        await run('CREATE INDEX idx_药品追溯码记录_追溯码 ON 药品追溯码记录(追溯码)');
        await run('CREATE INDEX idx_药品追溯码记录_药品编号 ON 药品追溯码记录(药品编号)');
        await run('CREATE INDEX idx_药品追溯码记录_操作类型 ON 药品追溯码记录(操作类型)');
        await run('CREATE INDEX idx_药品追溯码记录_销售订单编号 ON 药品追溯码记录(销售订单编号)');
        
        result.actions.push('创建药品追溯码记录表成功');
      } else {
        result.actions.push('药品追溯码记录表已存在');
      }

      // 3. 确保订单明细表有追溯码字段
      const orderDetailStructure = await query('PRAGMA table_info(订单明细)');
      const hasTraceCodeField = orderDetailStructure.some(col => col.name === '追溯码');
      
      if (!hasTraceCodeField) {
        console.log('为订单明细表添加追溯码字段...');
        await run('ALTER TABLE 订单明细 ADD COLUMN 追溯码 TEXT');
        result.actions.push('为订单明细表添加追溯码字段成功');
      } else {
        result.actions.push('订单明细表已有追溯码字段');
      }

      // 4. 确保订单明细表有批次编号字段
      const hasBatchIdField = orderDetailStructure.some(col => col.name === '批次编号');
      
      if (!hasBatchIdField) {
        console.log('为订单明细表添加批次编号字段...');
        await run('ALTER TABLE 订单明细 ADD COLUMN 批次编号 INTEGER');
        await run('CREATE INDEX idx_订单明细_批次编号 ON 订单明细(批次编号)');
        result.actions.push('为订单明细表添加批次编号字段成功');
      } else {
        result.actions.push('订单明细表已有批次编号字段');
      }

      // 提交事务
      await run('COMMIT');
      
      console.log('销售管理追溯码数据库表结构初始化完成');
      
      return NextResponse.json({
        success: true,
        message: '销售管理追溯码数据库表结构初始化成功',
        data: result
      });

    } catch (error) {
      // 回滚事务
      await run('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error('初始化销售管理追溯码数据库表结构失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '初始化销售管理追溯码数据库表结构失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
