import { NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

/**
 * 数据库表名中文化迁移API
 * 将 batch 表重命名为 药品批次表
 * 将 settings 表重命名为 系统设置表
 */
export async function POST() {
  try {
    console.log('开始数据库表名中文化迁移...');

    // 开始事务
    await run('BEGIN TRANSACTION');

    const result = {
      actions: [],
      errors: []
    };

    try {
      // 1. 检查并迁移 batch 表
      const batchTableExists = await query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='batch'"
      );

      if (batchTableExists && batchTableExists.length > 0) {
        console.log('发现 batch 表，开始迁移...');

        // 检查目标表是否已存在
        const targetBatchExists = await query(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='药品批次表'"
        );

        if (!targetBatchExists || targetBatchExists.length === 0) {
          // 创建新的中文表
          await run(`
            CREATE TABLE 药品批次表 (
              编号 INTEGER PRIMARY KEY AUTOINCREMENT,
              药品编号 INTEGER NOT NULL,
              批次号 TEXT NOT NULL,
              生产日期 DATE,
              有效期 DATE,
              数量 INTEGER NOT NULL DEFAULT 0,
              剩余数量 INTEGER NOT NULL DEFAULT 0,
              供应商编号 INTEGER,
              成本价 DECIMAL(10,2),
              状态 TEXT DEFAULT 'active' CHECK (状态 IN ('active', 'expired', 'depleted')),
              创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
              更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
              备注 TEXT,
              FOREIGN KEY (药品编号) REFERENCES 药品信息(编号),
              FOREIGN KEY (供应商编号) REFERENCES 供应商(编号)
            )
          `);

          // 迁移数据
          await run(`
            INSERT INTO 药品批次表 (
              编号, 药品编号, 批次号, 生产日期, 有效期, 数量, 剩余数量,
              供应商编号, 成本价, 状态, 创建时间, 更新时间, 备注
            )
            SELECT 
              编号, 药品编号, 批次号, 生产日期, 有效期, 数量, 剩余数量,
              供应商编号, 成本价, 状态, 创建时间, 更新时间, 备注
            FROM batch
          `);

          // 创建索引
          await run('CREATE INDEX idx_药品批次表_药品编号 ON 药品批次表(药品编号)');
          await run('CREATE INDEX idx_药品批次表_批次号 ON 药品批次表(批次号)');
          await run('CREATE INDEX idx_药品批次表_有效期 ON 药品批次表(有效期)');
          await run('CREATE INDEX idx_药品批次表_状态 ON 药品批次表(状态)');

          // 删除旧表
          await run('DROP TABLE batch');

          result.actions.push('batch 表已成功迁移为 药品批次表');
        } else {
          result.actions.push('药品批次表已存在，跳过 batch 表迁移');
        }
      } else {
        result.actions.push('batch 表不存在，无需迁移');
      }

      // 2. 检查并迁移 settings 表
      const settingsTableExists = await query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='settings'"
      );

      if (settingsTableExists && settingsTableExists.length > 0) {
        console.log('发现 settings 表，开始迁移...');

        // 检查目标表是否已存在
        const targetSettingsExists = await query(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='系统设置表'"
        );

        if (!targetSettingsExists || targetSettingsExists.length === 0) {
          // 创建新的中文表
          await run(`
            CREATE TABLE 系统设置表 (
              编号 INTEGER PRIMARY KEY AUTOINCREMENT,
              设置名称 TEXT NOT NULL UNIQUE,
              设置值 TEXT,
              描述 TEXT,
              创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
              更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
          `);

          // 迁移数据
          await run(`
            INSERT INTO 系统设置表 (编号, 设置名称, 设置值, 描述, 创建时间, 更新时间)
            SELECT id, setting_name, setting_value, description, created_at, updated_at
            FROM settings
          `);

          // 创建索引
          await run('CREATE UNIQUE INDEX idx_系统设置表_设置名称 ON 系统设置表(设置名称)');

          // 删除旧表
          await run('DROP TABLE settings');

          result.actions.push('settings 表已成功迁移为 系统设置表');
        } else {
          result.actions.push('系统设置表已存在，跳过 settings 表迁移');
        }
      } else {
        result.actions.push('settings 表不存在，无需迁移');
      }

      // 3. 更新批次视图（如果存在）
      try {
        await run('DROP VIEW IF EXISTS batch_view');
        await run(`
          CREATE VIEW batch_view AS
          SELECT 
            b.编号 as id,
            b.药品编号 as product_id,
            b.批次号 as batch_number,
            b.生产日期 as production_date,
            b.有效期 as expiry_date,
            b.数量 as quantity,
            b.剩余数量 as remaining_quantity,
            b.供应商编号 as supplier_id,
            s.名称 as supplier_name,
            b.成本价 as purchase_price,
            b.状态 as status,
            b.创建时间 as created_at,
            b.更新时间 as updated_at,
            b.备注 as notes,
            p.名称 as product_name,
            p.规格 as specification
          FROM 药品批次表 b
          LEFT JOIN 供应商 s ON b.供应商编号 = s.编号
          LEFT JOIN 药品信息 p ON b.药品编号 = p.编号
        `);
        result.actions.push('批次视图已更新为使用新的中文表名');
      } catch (viewError) {
        console.log('更新批次视图失败:', viewError);
        result.errors.push('更新批次视图失败: ' + viewError.message);
      }

      // 提交事务
      await run('COMMIT');

      console.log('数据库表名中文化迁移完成');

      return NextResponse.json({
        success: true,
        message: '数据库表名中文化迁移成功',
        data: result
      });

    } catch (error) {
      // 回滚事务
      await run('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error('数据库表名中文化迁移失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '数据库表名中文化迁移失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
