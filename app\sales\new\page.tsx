'use client';

import Link from "next/link";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import AddMedicineModal from "@/app/components/AddMedicineModal";
import SalesBarcodeScanner from "@/app/components/SalesBarcodeScanner";
import SalesTraceCodeScanner from "@/app/components/SalesTraceCodeScanner";
import AlertDialog, { AlertType } from "@/app/components/AlertDialog";

interface Medicine {
  id: number;
  name: string;
  specification: string;
  manufacturer: string;
  price: number;
  stock_quantity: number;
  category_name: string;
  barcode?: string;
  trace_code?: string;
}

interface BatchInfo {
  batchNo: string;
  expireDate: string;
  productionDate?: string;
  manufacturer: string;
  drugName: string;
  specification: string;
  approvalNumber?: string;
  barcode?: string;
  localBatchId?: number;
  localBatchQuantity?: number;
}

interface OrderItem {
  medicine: Medicine;
  quantity: number;
  subtotal: number;
  traceCode?: string;
  batchId?: number;
  batchInfo?: BatchInfo;
}

interface PaymentInfo {
  totalAmount: number;
  discountAmount: number;
  payableAmount: number;
  receivedAmount: number;
  changeAmount: number;
  paymentMethod: string;
}

interface SystemSettings {
  storeName: string;
  orderNumberDigits: number;
}

export default function NewSalesPage() {
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isContinuousMode, setIsContinuousMode] = useState(false);
  const [isBarcodeScannerActive, setIsBarcodeScannerActive] = useState(true); // 默认激活扫码功能
  const [isTraceCodeScannerOpen, setIsTraceCodeScannerOpen] = useState(false); // 追溯码扫描器状态
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);
  const [settings, setSettings] = useState<SystemSettings>({
    storeName: '药店零售管理系统',
    orderNumberDigits: 4
  });
  const [orderNumber, setOrderNumber] = useState('');

  // 支付信息状态
  const [discountAmount, setDiscountAmount] = useState(0);
  const [receivedAmount, setReceivedAmount] = useState(0);
  const [paymentMethod, setPaymentMethod] = useState('现金');
  const [insufficientPayment, setInsufficientPayment] = useState(false);

  // 错误提示弹窗状态
  const [isAlertOpen, setIsAlertOpen] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertType, setAlertType] = useState<AlertType>('error');

  // 取消确认对话框状态
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);

  // 获取系统设置并生成订单编号
  useEffect(() => {
    const fetchSettingsAndGenerateOrderNumber = async () => {
      try {
        // 首先尝试从API获取系统设置
        const response = await fetch('/api/settings');
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            const apiSettings: SystemSettings = {
              storeName: data.data.storeName || '药店零售管理系统',
              orderNumberDigits: parseInt(data.data.orderNumberDigits) || 4
            };
            setSettings(apiSettings);

            // 使用API设置生成订单编号
            generateOrderNumberWithSettings(apiSettings);
            return;
          }
        }

        // API失败时，从本地存储获取设置
        if (typeof window !== 'undefined') {
          try {
            const savedSettings = localStorage.getItem('systemSettings');
            if (savedSettings) {
              const parsedSettings = JSON.parse(savedSettings);
              setSettings(parsedSettings);
              generateOrderNumberWithSettings(parsedSettings);
              return;
            }
          } catch (e) {
            console.error('加载本地设置失败:', e);
          }
        }

        // 使用默认设置生成订单编号
        generateOrderNumberWithSettings(settings);
      } catch (error) {
        console.error('获取系统设置失败:', error);
        // 使用默认设置生成订单编号
        generateOrderNumberWithSettings(settings);
      }
    };

    fetchSettingsAndGenerateOrderNumber();
  }, []);

  // 生成订单编号（使用指定设置）
  const generateOrderNumberWithSettings = async (settingsToUse: SystemSettings) => {
    try {
      // 尝试从API获取今日订单序号
      const response = await fetch('/api/orders/next-number?type=SO');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setOrderNumber(data.data.orderNumber);
          return;
        }
      }
    } catch (error) {
      console.error('获取订单序号失败，使用默认序号:', error);
    }

    // API失败时，使用本地生成
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const dateStr = `${year}${month}${day}`;

    // 生成指定位数的序列号（默认为1）
    const sequenceNumber = '1'.padStart(settingsToUse.orderNumberDigits, '0');

    // 销售订单固定使用SO前缀
    const newOrderNumber = `SO-${dateStr}${sequenceNumber}`;
    setOrderNumber(newOrderNumber);
  };

  // 生成订单编号（使用当前设置）
  const generateOrderNumber = () => {
    generateOrderNumberWithSettings(settings);
  };

  // 计算订单总计
  const calculateTotal = () => {
    return orderItems.reduce((sum, item) => sum + item.subtotal, 0);
  };

  // 计算应付金额
  const calculatePayableAmount = () => {
    return calculateTotal() - discountAmount;
  };

  // 计算找零金额
  const calculateChangeAmount = () => {
    const payableAmount = calculatePayableAmount();
    return receivedAmount > payableAmount ? receivedAmount - payableAmount : 0;
  };

  // 处理收款金额变更
  const handleReceivedAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) || 0;
    setReceivedAmount(value);

    // 检查收款金额是否足够
    setInsufficientPayment(value < calculatePayableAmount());
  };

  // 处理优惠金额变更
  const handleDiscountAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) || 0;
    setDiscountAmount(value);

    // 更新收款金额为新的应付金额
    const newPayableAmount = calculateTotal() - value;
    setReceivedAmount(newPayableAmount);
  };

  // 添加药品到订单
  const handleAddMedicine = (medicine: Medicine, quantity: number) => {
    // 检查是否已存在该药品
    const existingItemIndex = orderItems.findIndex(item => item.medicine.id === medicine.id);

    if (existingItemIndex !== -1) {
      // 已存在，更新数量
      const updatedItems = [...orderItems];
      const newQuantity = updatedItems[existingItemIndex].quantity + quantity;
      updatedItems[existingItemIndex] = {
        ...updatedItems[existingItemIndex],
        quantity: newQuantity,
        subtotal: medicine.price * newQuantity
      };
      setOrderItems(updatedItems);
    } else {
      // 不存在，添加新项
      setOrderItems([
        ...orderItems,
        {
          medicine,
          quantity,
          subtotal: medicine.price * quantity
        }
      ]);
    }
  };

  // 更新商品数量
  const updateQuantity = (index: number, newQuantity: number) => {
    if (newQuantity <= 0) return;

    const updatedItems = [...orderItems];
    updatedItems[index] = {
      ...updatedItems[index],
      quantity: newQuantity,
      subtotal: updatedItems[index].medicine.price * newQuantity
    };

    setOrderItems(updatedItems);
  };

  // 移除商品
  const removeItem = (index: number) => {
    setOrderItems(orderItems.filter((_, i) => i !== index));
  };

  // 监听订单项变化，更新收款金额
  useEffect(() => {
    // 更新收款金额为应付金额
    setReceivedAmount(calculatePayableAmount());
  }, [orderItems, discountAmount]);

  // 计算商品总数
  const totalItems = orderItems.reduce((sum, item) => sum + item.quantity, 0);

  // 检查是否有未保存的数据
  const hasUnsavedData = () => {
    // 检查是否有订单项
    if (orderItems.length > 0) return true;

    // 检查客户信息
    const customerName = document.getElementById('customerName') as HTMLInputElement;
    const customerPhone = document.getElementById('customerPhone') as HTMLInputElement;
    if (customerName?.value || customerPhone?.value) return true;

    // 检查优惠金额
    if (discountAmount > 0) return true;

    // 检查备注信息
    const note = document.getElementById('orderNote') as HTMLTextAreaElement;
    if (note?.value) return true;

    return false;
  };

  // 处理取消操作
  const handleCancel = () => {
    if (hasUnsavedData()) {
      // 有未保存数据，显示确认对话框
      setShowCancelConfirm(true);
    } else {
      // 没有数据，直接跳转
      router.push('/sales');
    }
  };

  // 确认取消并清空数据
  const confirmCancel = () => {
    // 清空所有状态
    setOrderItems([]);
    setDiscountAmount(0);
    setReceivedAmount(0);
    setPaymentMethod('现金');
    setInsufficientPayment(false);

    // 清空表单数据
    const customerName = document.getElementById('customerName') as HTMLInputElement;
    const customerPhone = document.getElementById('customerPhone') as HTMLInputElement;
    const note = document.getElementById('orderNote') as HTMLTextAreaElement;
    const memberCheckbox = document.querySelector('input[type="checkbox"]') as HTMLInputElement;

    if (customerName) customerName.value = '';
    if (customerPhone) customerPhone.value = '';
    if (note) note.value = '';
    if (memberCheckbox) memberCheckbox.checked = false;

    // 关闭确认对话框
    setShowCancelConfirm(false);

    // 跳转到销售管理页面
    router.push('/sales');
  };

  return (
    <div className="flex flex-col h-full gap-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Link href="/sales" className="text-blue-600 hover:text-blue-800">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
          </Link>
          <h1 className="text-2xl font-bold text-gray-800">新建销售单</h1>
        </div>
        <div className="flex gap-3">
          <button
            onClick={async () => {
              if (orderItems.length === 0) {
                setAlertType('warning');
                setAlertMessage('请先添加药品到订单中');
                setIsAlertOpen(true);
                return;
              }

              try {
                // 获取表单数据
                const customerName = document.getElementById('customerName') as HTMLInputElement;
                const customerPhone = document.getElementById('customerPhone') as HTMLInputElement;
                const paymentMethod = document.getElementById('paymentMethod') as HTMLSelectElement;
                const note = document.getElementById('orderNote') as HTMLTextAreaElement;

                // 构建请求数据
                const orderData = {
                  orderNumber,
                  customer: {
                    name: customerName?.value || '',
                    phone: customerPhone?.value || ''
                  },
                  items: orderItems,
                  totalAmount: calculateTotal(),
                  discountAmount: discountAmount,
                  payableAmount: calculatePayableAmount(),
                  receivedAmount: receivedAmount,
                  changeAmount: calculateChangeAmount(),
                  paymentMethod: paymentMethod,
                  note: note?.value || ''
                };

                // 调用API保存订单
                const response = await fetch('/api/orders', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify(orderData)
                });

                const result = await response.json();

                if (result.success) {
                  setAlertType('success');
                  setAlertMessage('订单保存成功！订单号：' + orderNumber);
                  setIsAlertOpen(true);
                  // 跳转到销售管理页面
                  setTimeout(() => {
                    window.location.href = '/sales';
                  }, 1500);
                } else {
                  setAlertType('error');
                  setAlertMessage('保存失败：' + result.message);
                  setIsAlertOpen(true);
                }
              } catch (error) {
                console.error('保存订单失败:', error);
                setAlertType('error');
                setAlertMessage('保存订单失败，请重试');
                setIsAlertOpen(true);
              }
            }}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition flex items-center gap-1"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
            </svg>
            保存
          </button>
          <button
            onClick={handleCancel}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition flex items-center gap-1"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
            取消
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-medium text-gray-800 mb-4">订单信息</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">订单编号</label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-gray-50 text-blue-700"
                  value={orderNumber}
                  readOnly
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">订单日期</label>
                <input
                  type="date"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-blue-700"
                  defaultValue={new Date().toISOString().split('T')[0]}
                />
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-md font-medium text-gray-800 mb-2">客户信息</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">客户姓名</label>
                  <input
                    id="customerName"
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-blue-700"
                    placeholder="输入客户姓名"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">联系电话</label>
                  <input
                    id="customerPhone"
                    type="tel"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-blue-700"
                    placeholder="输入联系电话"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1">
                    <input type="checkbox" className="rounded text-blue-500 focus:ring-blue-500"/>
                    建立会员信息
                  </label>
                </div>
              </div>
            </div>

            <div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-md font-medium text-gray-800">药品清单</h3>
                <div className="flex items-center gap-3">
                  <div className="flex items-center">
                    <label className="inline-flex items-center cursor-pointer mr-4">
                      <input
                        type="checkbox"
                        checked={isBarcodeScannerActive}
                        onChange={(e) => setIsBarcodeScannerActive(e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="relative w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-4 peer-focus:ring-blue-300 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      <span className="ms-3 text-sm font-medium text-gray-700">扫码模式</span>
                    </label>
                  </div>
                  <button
                    className="text-blue-600 hover:text-blue-800 text-sm flex items-center gap-1"
                    onClick={() => {
                      setIsContinuousMode(true);
                      setIsModalOpen(true);
                    }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    连续添加
                  </button>
                  <button
                    className="text-blue-600 hover:text-blue-800 text-sm flex items-center gap-1"
                    onClick={() => {
                      setIsContinuousMode(false);
                      setIsModalOpen(true);
                    }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    添加药品
                  </button>
                </div>
              </div>

              <div className="overflow-x-auto border border-gray-300 rounded-md mb-4">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">药品名称</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规格</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单价</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">小计</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {orderItems.length > 0 ? (
                      orderItems.map((item, index) => (
                        <tr key={index}>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-blue-700 font-medium">{item.medicine.name}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{item.medicine.specification}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-blue-700">¥{item.medicine.price.toFixed(2)}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm">
                            <div className="flex items-center border border-gray-300 rounded-md w-24">
                              <button
                                className="px-2 py-1 text-gray-500 hover:text-gray-700"
                                onClick={() => updateQuantity(index, item.quantity - 1)}
                              >
                                -
                              </button>
                              <input
                                type="number"
                                className="w-full text-center border-0 focus:ring-0 text-blue-700"
                                value={item.quantity}
                                onChange={(e) => updateQuantity(index, parseInt(e.target.value) || 1)}
                                min="1"
                                max={item.medicine.stock_quantity}
                              />
                              <button
                                className="px-2 py-1 text-gray-500 hover:text-gray-700"
                                onClick={() => updateQuantity(index, item.quantity + 1)}
                              >
                                +
                              </button>
                            </div>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-blue-700 font-medium">¥{item.subtotal.toFixed(2)}</td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm">
                            <button
                              className="text-red-600 hover:text-red-800"
                              onClick={() => removeItem(index)}
                            >
                              删除
                            </button>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={6} className="px-4 py-3 text-center text-sm text-gray-500">
                          尚未添加任何药品，请点击"添加药品"按钮添加
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {orderItems.length === 0 && (
                <div className="flex gap-4 items-center text-sm mb-3">
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-2 flex items-center gap-1 text-blue-700">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    点击右上角"添加药品"按钮添加更多药品
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-medium text-gray-800 mb-4">结算信息</h2>

            <div className="space-y-3 mb-6">
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-600">商品总数:</span>
                <span className="font-medium text-blue-700">{totalItems} 件</span>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-600">商品金额:</span>
                <span className="font-medium text-blue-700">¥{calculateTotal().toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-600">优惠金额:</span>
                <div className="flex items-center">
                  <span className="font-medium text-red-600 mr-2">-¥{discountAmount.toFixed(2)}</span>
                  <input
                    type="number"
                    min="0"
                    max={calculateTotal()}
                    step="0.01"
                    value={discountAmount}
                    onChange={handleDiscountAmountChange}
                    className="w-20 px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-blue-700 text-sm"
                    placeholder="优惠"
                  />
                </div>
              </div>
              <div className="pt-3 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <span className="text-gray-800 font-medium">应付金额:</span>
                  <span className="text-xl font-bold text-blue-700">¥{calculatePayableAmount().toFixed(2)}</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">支付方式</label>
                <select
                  id="paymentMethod"
                  value={paymentMethod}
                  onChange={(e) => setPaymentMethod(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-blue-700"
                >
                  <option value="现金">现金支付</option>
                  <option value="微信">微信支付</option>
                  <option value="支付宝">支付宝</option>
                  <option value="银行卡">银行卡</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">收款金额</label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={receivedAmount}
                  onChange={handleReceivedAmountChange}
                  className={`w-full px-3 py-2 border ${insufficientPayment ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-blue-700`}
                  placeholder="输入收款金额"
                />
                {insufficientPayment && (
                  <p className="mt-1 text-sm text-red-600">收款金额不足</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">找零金额</label>
                <div className="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-blue-700 font-medium">
                  ¥{calculateChangeAmount().toFixed(2)}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">备注信息</label>
                <textarea
                  id="orderNote"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-blue-700"
                  rows={3}
                  placeholder="输入备注信息（可选）"
                />
              </div>
            </div>
          </div>

          <div className="mt-4 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-md font-medium text-gray-800 mb-3">快捷操作</h3>
            <div className="space-y-3">
              <button
                onClick={async () => {
                  if (orderItems.length === 0) {
                    setAlertType('warning');
                    setAlertMessage('请先添加药品到订单中');
                    setIsAlertOpen(true);
                    return;
                  }

                  try {
                    // 获取表单数据
                    const customerName = document.getElementById('customerName') as HTMLInputElement;
                    const customerPhone = document.getElementById('customerPhone') as HTMLInputElement;
                    const paymentMethod = document.getElementById('paymentMethod') as HTMLSelectElement;
                    const note = document.getElementById('orderNote') as HTMLTextAreaElement;

                    // 构建请求数据
                    const orderData = {
                      orderNumber,
                      customer: {
                        name: customerName?.value || '',
                        phone: customerPhone?.value || ''
                      },
                      items: orderItems,
                      totalAmount: calculateTotal(),
                      discountAmount: discountAmount,
                      payableAmount: calculatePayableAmount(),
                      receivedAmount: receivedAmount,
                      changeAmount: calculateChangeAmount(),
                      paymentMethod: paymentMethod,
                      note: note?.value || ''
                    };

                    // 调用API保存订单
                    const response = await fetch('/api/orders', {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json'
                      },
                      body: JSON.stringify(orderData)
                    });

                    const result = await response.json();

                    if (result.success) {
                      setAlertType('success');
                      setAlertMessage('订单结算成功！订单号：' + orderNumber);
                      setIsAlertOpen(true);
                      // 成功后延迟跳转，让用户看到成功提示
                      setTimeout(() => {
                        window.location.href = '/sales';
                      }, 1500);
                    } else {
                      setAlertType('error');
                      setAlertMessage('订单保存失败：' + result.message);
                      setIsAlertOpen(true);
                    }
                  } catch (error) {
                    console.error('保存订单失败:', error);
                    setAlertType('error');
                    setAlertMessage('保存订单失败，请重试');
                    setIsAlertOpen(true);
                  }
                }}
                className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                完成结算并打印
              </button>
              <button
                onClick={async () => {
                  if (orderItems.length === 0) {
                    setAlertType('warning');
                    setAlertMessage('请先添加药品到订单中');
                    setIsAlertOpen(true);
                    return;
                  }

                  try {
                    // 获取表单数据
                    const customerName = document.getElementById('customerName') as HTMLInputElement;
                    const customerPhone = document.getElementById('customerPhone') as HTMLInputElement;
                    const paymentMethod = document.getElementById('paymentMethod') as HTMLSelectElement;
                    const note = document.getElementById('orderNote') as HTMLTextAreaElement;

                    // 构建请求数据
                    const orderData = {
                      orderNumber,
                      customer: {
                        name: customerName?.value || '',
                        phone: customerPhone?.value || ''
                      },
                      items: orderItems,
                      totalAmount: calculateTotal(),
                      discountAmount: discountAmount,
                      payableAmount: calculatePayableAmount(),
                      receivedAmount: receivedAmount,
                      changeAmount: calculateChangeAmount(),
                      paymentMethod: paymentMethod,
                      note: note?.value || '',
                      status: '草稿' // 设置状态为草稿
                    };

                    // 调用API保存订单
                    const response = await fetch('/api/orders', {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json'
                      },
                      body: JSON.stringify(orderData)
                    });

                    const result = await response.json();

                    if (result.success) {
                      setAlertType('success');
                      setAlertMessage('订单已保存为草稿！订单号：' + orderNumber);
                      setIsAlertOpen(true);
                      // 成功后延迟跳转，让用户看到成功提示
                      setTimeout(() => {
                        window.location.href = '/sales';
                      }, 1500);
                    } else {
                      setAlertType('error');
                      setAlertMessage('保存草稿失败：' + result.message);
                      setIsAlertOpen(true);
                    }
                  } catch (error) {
                    console.error('保存草稿失败:', error);
                    setAlertType('error');
                    setAlertMessage('保存草稿失败，请重试');
                    setIsAlertOpen(true);
                  }
                }}
                className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                </svg>
                保存为草稿
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 添加药品弹窗 */}
      <AddMedicineModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setIsContinuousMode(false);
        }}
        onAdd={handleAddMedicine}
        continuousMode={isContinuousMode}
      />

      {/* 扫码添加药品组件 */}
      <SalesBarcodeScanner
        isActive={isBarcodeScannerActive}
        onAddMedicine={handleAddMedicine}
      />

      {/* 错误提示弹窗 */}
      <AlertDialog
        isOpen={isAlertOpen}
        onClose={() => setIsAlertOpen(false)}
        message={alertMessage}
        type={alertType}
      />

      {/* 取消确认对话框 */}
      {showCancelConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  <svg className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-gray-900">确认取消</h3>
                </div>
              </div>
              <div className="mb-4">
                <p className="text-sm text-gray-500">
                  您有未保存的订单数据，取消操作将丢失所有已填写的信息。确定要取消吗？
                </p>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowCancelConfirm(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  继续编辑
                </button>
                <button
                  onClick={confirmCancel}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  确定取消
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}