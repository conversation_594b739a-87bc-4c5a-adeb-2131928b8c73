import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

/**
 * 获取即将过期的批次
 * GET /api/inventory/expiring-batches?days=30
 */
export async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const days = parseInt(searchParams.get('days') || '30'); // 默认30天内过期
    
    // 计算过期日期阈值
    const today = new Date();
    const expiryThreshold = new Date();
    expiryThreshold.setDate(today.getDate() + days);
    
    const todayStr = today.toISOString().split('T')[0];
    const thresholdStr = expiryThreshold.toISOString().split('T')[0];
    
    // 查询即将过期的批次
    const expiringBatches = await query(`
      SELECT 
        b.编号 as id,
        b.药品编号 as product_id,
        b.批次号 as batch_number,
        b.生产日期 as production_date,
        b.有效期 as expiry_date,
        b.数量 as quantity,
        b.剩余数量 as remaining_quantity,
        b.供应商编号 as supplier_id,
        s.名称 as supplier_name,
        b.成本价 as purchase_price,
        b.状态 as status,
        b.创建时间 as created_at,
        b.更新时间 as updated_at,
        p.名称 as product_name,
        p.规格 as specification,
        p.剂型 as dosage_form,
        p.生产厂家 as manufacturer,
        -- 计算距离过期的天数
        CAST((julianday(b.有效期) - julianday('now')) AS INTEGER) as days_to_expiry
      FROM batch b
      LEFT JOIN 供应商 s ON b.供应商编号 = s.编号
      LEFT JOIN 药品信息 p ON b.药品编号 = p.编号
      WHERE b.状态 = 'active' 
        AND b.剩余数量 > 0
        AND b.有效期 IS NOT NULL
        AND b.有效期 >= ?
        AND b.有效期 <= ?
      ORDER BY b.有效期 ASC, b.剩余数量 DESC
    `, [todayStr, thresholdStr]);
    
    // 分类批次：已过期、即将过期（7天内）、临期（30天内）
    const categorizedBatches = {
      expired: [] as any[],
      critical: [] as any[], // 7天内过期
      warning: [] as any[],   // 30天内过期
      total_count: expiringBatches.length,
      total_value: 0
    };
    
    let totalValue = 0;
    
    for (const batch of expiringBatches) {
      const daysToExpiry = batch.days_to_expiry;
      const batchValue = (batch.remaining_quantity || 0) * (batch.purchase_price || 0);
      totalValue += batchValue;
      
      // 添加计算字段
      batch.batch_value = batchValue;
      batch.expiry_status = daysToExpiry < 0 ? 'expired' : 
                           daysToExpiry <= 7 ? 'critical' : 'warning';
      
      if (daysToExpiry < 0) {
        categorizedBatches.expired.push(batch);
      } else if (daysToExpiry <= 7) {
        categorizedBatches.critical.push(batch);
      } else {
        categorizedBatches.warning.push(batch);
      }
    }
    
    categorizedBatches.total_value = totalValue;
    
    return NextResponse.json({
      success: true,
      data: categorizedBatches
    });
    
  } catch (error) {
    console.error('获取即将过期批次失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取即将过期批次失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
