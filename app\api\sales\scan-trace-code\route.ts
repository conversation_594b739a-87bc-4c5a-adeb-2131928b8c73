import { NextRequest, NextResponse } from 'next/server';
import { query, get, run } from '@/lib/db';

/**
 * 销售管理中扫描药品追溯码API
 * 扫描追溯码，获取药品信息，并准备出库操作
 * POST /api/sales/scan-trace-code
 */
export async function POST(request: NextRequest) {
  try {
    const { traceCode, orderId } = await request.json();

    if (!traceCode) {
      return NextResponse.json({
        success: false,
        message: '请提供追溯码'
      }, { status: 400 });
    }

    console.log('销售扫描追溯码:', traceCode);

    // 1. 检查追溯码是否已被使用（已出库）
    const existingRecord = await get(
      `SELECT 
        编号, 药品编号, 操作类型, 操作时间, 销售订单编号
      FROM 药品追溯码记录 
      WHERE 追溯码 = ? AND 操作类型 IN ('出库', '销售')`,
      [traceCode]
    );

    if (existingRecord) {
      return NextResponse.json({
        success: false,
        message: `该追溯码已被使用（${existingRecord.操作类型}），操作时间：${existingRecord.操作时间}`,
        data: {
          isUsed: true,
          existingRecord
        }
      }, { status: 400 });
    }

    // 2. 调用码上放心平台获取药品信息
    let drugInfo = null;
    try {
      const drugResponse = await fetch(`${request.nextUrl.origin}/api/mashangfangxin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'getDrugInfoByTraceCode',
          params: { code: traceCode }
        }),
      });

      if (drugResponse.ok) {
        const drugData = await drugResponse.json();
        if (drugData.success) {
          drugInfo = drugData.data;
        }
      }
    } catch (error) {
      console.error('获取药品追溯信息失败:', error);
    }

    if (!drugInfo) {
      return NextResponse.json({
        success: false,
        message: '无法获取药品追溯信息，请检查追溯码是否正确'
      }, { status: 400 });
    }

    // 3. 解析药品信息
    const codeFullInfo = drugInfo?.result?.models?.code_full_info_dto?.[0];
    if (!codeFullInfo) {
      return NextResponse.json({
        success: false,
        message: '追溯码信息格式错误'
      }, { status: 400 });
    }

    const drugDetails = codeFullInfo.drug_ent_base_d_t_o || {};
    const batchInfo = {
      batchNo: codeFullInfo.batch_no || '',
      expireDate: codeFullInfo.expire_date || '',
      productionDate: codeFullInfo.production_date || '',
      manufacturer: drugDetails.ent_name || '',
      drugName: drugDetails.physic_name || '',
      specification: drugDetails.pkg_spec_crit || '',
      approvalNumber: drugDetails.approval_licence_no || '',
      barcode: codeFullInfo.barcode || ''
    };

    // 4. 在本地数据库中查找对应的药品
    let localProduct = null;
    
    // 首先尝试通过条形码查找
    if (batchInfo.barcode) {
      localProduct = await get(
        'SELECT * FROM 药品信息 WHERE 条形码 = ?',
        [batchInfo.barcode]
      );
    }

    // 如果通过条形码没找到，尝试通过药品名称查找
    if (!localProduct && batchInfo.drugName) {
      localProduct = await get(
        'SELECT * FROM 药品信息 WHERE 名称 LIKE ? OR 通用名 LIKE ?',
        [`%${batchInfo.drugName}%`, `%${batchInfo.drugName}%`]
      );
    }

    // 如果通过批准文号查找
    if (!localProduct && batchInfo.approvalNumber) {
      localProduct = await get(
        'SELECT * FROM 药品信息 WHERE 批准文号 = ?',
        [batchInfo.approvalNumber]
      );
    }

    if (!localProduct) {
      return NextResponse.json({
        success: false,
        message: '在本地数据库中未找到对应的药品，请先在药品管理中添加该药品',
        data: {
          traceCodeInfo: batchInfo,
          suggestions: {
            name: batchInfo.drugName,
            manufacturer: batchInfo.manufacturer,
            specification: batchInfo.specification,
            approvalNumber: batchInfo.approvalNumber,
            barcode: batchInfo.barcode
          }
        }
      }, { status: 404 });
    }

    // 5. 检查库存
    if (localProduct.库存数量 <= 0) {
      return NextResponse.json({
        success: false,
        message: `药品 ${localProduct.名称} 库存不足，当前库存：${localProduct.库存数量}`
      }, { status: 400 });
    }

    // 6. 查找对应的批次信息
    let batchRecord = null;
    if (batchInfo.batchNo) {
      batchRecord = await get(
        'SELECT * FROM batch WHERE 药品编号 = ? AND 批次号 = ? AND 状态 = "active" AND 剩余数量 > 0',
        [localProduct.编号, batchInfo.batchNo]
      );
    }

    // 7. 返回药品信息和批次信息，准备添加到销售订单
    return NextResponse.json({
      success: true,
      message: '追溯码扫描成功',
      data: {
        traceCode,
        localProduct: {
          id: localProduct.编号,
          name: localProduct.名称,
          genericName: localProduct.通用名,
          specification: localProduct.规格,
          manufacturer: localProduct.生产厂家,
          price: localProduct.售价,
          stockQuantity: localProduct.库存数量,
          barcode: localProduct.条形码,
          approvalNumber: localProduct.批准文号
        },
        batchInfo: {
          ...batchInfo,
          localBatchId: batchRecord?.编号 || null,
          localBatchQuantity: batchRecord?.剩余数量 || 0
        },
        canSell: true,
        quantity: 1 // 默认销售数量为1
      }
    });

  } catch (error) {
    console.error('销售扫描追溯码失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '扫描追溯码失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
